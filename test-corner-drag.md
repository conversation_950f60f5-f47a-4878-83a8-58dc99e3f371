# 图片裁剪功能实现说明 (真正的裁剪，图片不变大)

## 功能概述
已成功实现真正的图片裁剪功能，主要特点：

1. **四个角拖动点**: 在 `editLine == 1` 模式下，图片四个角会显示蓝色拖动点
2. **真正的裁剪**: 图片本身尺寸保持不变，只改变显示的裁剪区域
3. **裁剪偏移**: 通过 `cropX` 和 `cropY` 控制图片的裁剪起始位置
4. **最大限制**: 裁剪区域最大不能超过图片原本大小
5. **最小限制**: 显示区域最小为 20px
6. **CSS裁剪**: 使用 `object-fit: none` 和 `object-position` 实现真正的图片裁剪

## 修复的问题

### 左下角拖动修复
- **问题**: 之前左下角拖动时Y坐标处理不正确
- **修复**: 重新设计了锚点逻辑，确保每个角都有正确的固定点
- **效果**: 现在左下角拖动时，右上角保持固定，只调整左边和下边

## 四个角的拖动逻辑

### 1. 左上角 (top-left)
- **固定点**: 右下角
- **调整**: 左边界和上边界
- **效果**: 向内拖动缩小裁剪区域，向外拖动扩大裁剪区域

### 2. 右上角 (top-right)
- **固定点**: 左下角
- **调整**: 右边界和上边界
- **效果**: 水平向右、垂直向上调整裁剪区域

### 3. 左下角 (bottom-left) ✅ 已修复
- **固定点**: 右上角
- **调整**: 左边界和下边界
- **效果**: 水平向左、垂直向下调整裁剪区域

### 4. 右下角 (bottom-right)
- **固定点**: 左上角
- **调整**: 右边界和下边界
- **效果**: 向外拖动扩大裁剪区域

## 技术实现

### 核心算法 - 真正的图片裁剪
```javascript
// 以左下角为例 - 图片裁剪实现
case 'bottom-left':
    const rightBoundary = startPosX + startWidth;  // 右边界固定

    newDisplayX = Math.max(0, startPosX + deltaX);      // 显示区域左边调整
    newDisplayWidth = rightBoundary - newDisplayX;      // 显示宽度
    newDisplayHeight = Math.max(minSize, startHeight + deltaY); // 显示高度

    // 关键：调整裁剪偏移，实现真正的图片裁剪
    newCropX = startCropX + (newDisplayX - startPosX);  // 裁剪X偏移
    // cropY 保持不变，因为只调整左边和下边
```

### 图片显示实现
```vue
<image :style="{
    width: it.w + 'px',
    height: it.h + 'px',
    objectFit: 'none',  // 关键：不缩放图片
    objectPosition: `${-(it.cropX || 0)}px ${-(it.cropY || 0)}px`  // 裁剪偏移
}" :src="it.url" />
```

### 边界处理
- 最小尺寸限制：20px
- 最大尺寸限制：原图尺寸
- 容器边界限制：不超出外层容器
- 锚点重新计算：确保固定点始终正确

## 测试建议

1. **左下角测试**: 拖动左下角，确认右上角保持固定
2. **边界测试**: 拖动到容器边界，确认不会超出
3. **最小尺寸测试**: 拖动到很小，确认不会小于20px
4. **最大尺寸测试**: 拖动到很大，确认不会超过原图尺寸
