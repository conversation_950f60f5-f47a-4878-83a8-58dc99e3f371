"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  props: {
    dataarray: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      // 记录元素位置
      position: {
        x: 50,
        y: 50
      },
      // 记录触摸起点
      startPoint: {
        x: 0,
        y: 0
      },
      // 元素初始位置
      initialPosition: {
        x: 0,
        y: 0
      },
      // 容器和元素尺寸（使用固定值，适合小程序）
      dimensions: {
        outerWidth: 375,
        // 600rpx / 2
        outerHeight: 300,
        // 600rpx / 2
        // 移动块尺寸
        contentWidth: 100,
        // 200rpx / 2
        contentHeight: 50
        // 100rpx / 2
      },
      // 是否正在拖动
      isDragging: false,
      isRotate: 10,
      dataType: [],
      record: [],
      dataBack: {},
      editLine: 0,
      del: "https://bpic.588ku.com/element_origin_min_pic/19/04/09/9ce46a196fe52024a687e6aed9ab40a5.jpg",
      jia: "https://pic.616pic.com/ys_img/00/04/79/ISEtXRcNdo.jpg",
      rotate: "https://ts3.tc.mm.bing.net/th/id/OIP-C.CYvN0JBCeEE4pu5UQpx_FQHaHa?r=0&rs=1&pid=ImgDetMain&o=7&rm=3",
      suof: "https://ts1.tc.mm.bing.net/th/id/R-C.********************************?rik=y3JH%2bd3VUY9kpA&riu=http%3a%2f%2fbpic.588ku.com%2felement_pic%2f00%2f94%2f84%2f0156f2d0c0bc6b8.jpg&ehk=U1IKwhy8Bi5%2bXN1ptz4T71vgiaNDTB7tCz54ZrMVGSs%3d&risl=&pid=ImgRaw&r=0"
    };
  },
  mounted() {
    console.log(this.dataarray, "移动组件已加载");
    this.dataType = this.dataarray;
    common_vendor.index.$on("drawLines", (e) => {
      console.log(e, "fff");
      this.dataType.push({
        ids: this.dataType.length + 1,
        ...e
      });
    });
    common_vendor.index.$on("edit", (e) => {
      if (e) {
        this.editLine = e.id;
        console.log(e.id, "id");
      }
    });
    common_vendor.index.$on("selectScaleItem", (e) => {
      console.log(e, "aaa");
      this.dataType.push({
        ids: this.dataType.length + 1,
        ...e
      });
    });
  },
  methods: {
    // 边框拖动调整图片显示范围（裁剪区域），移动整体不缩放，仅改变显示区域
    handleTouchs(e, it, yar) {
      if (e && e.touches && e.touches.length === 1) {
        var img = this.findFiles(it);
        const touch = e.touches[0];
        const x = touch.clientX || touch.pageX;
        const y = touch.clientY || touch.pageY;
        if (!img._lastTouch) {
          img._lastTouch = { x, y };
          return;
        }
        const dx = x - img._lastTouch.x;
        const dy = y - img._lastTouch.y;
        let newW = img.w + dx;
        let newH = img.h + dy;
        newW = Math.max(20, Math.min(newW, this.dimensions.outerWidth));
        newH = Math.max(20, Math.min(newH, this.dimensions.outerHeight));
        img.w = newW;
        img.h = newH;
        img._lastTouch = { x, y };
        this.$forceUpdate();
      }
    },
    // 触摸开始
    handleTouchStart(e, item) {
      if (!e.touches || e.touches.length === 0)
        return;
      this.isDragging = true;
      this.startPoint = {
        x: e.touches[0].pageX || e.touches[0].clientX,
        y: e.touches[0].pageY || e.touches[0].clientY
      };
      this.initialPosition = {
        x: item.x,
        y: item.y
      };
      this.disp(item);
    },
    // 触摸移动
    handleTouchMove(e, item) {
      if (!e.touches || e.touches.length === 0)
        return;
      e.preventDefault();
      e.stopPropagation();
      const currentX = e.touches[0].pageX || e.touches[0].clientX;
      const currentY = e.touches[0].pageY || e.touches[0].clientY;
      const moveX = currentX - this.startPoint.x;
      const moveY = currentY - this.startPoint.y;
      let newX = this.initialPosition.x + moveX;
      let newY = this.initialPosition.y + moveY;
      const maxX = this.dimensions.outerWidth - item.w;
      const maxY = this.dimensions.outerHeight - item.h;
      newX = Math.max(0, Math.min(newX, maxX));
      newY = Math.max(0, Math.min(newY, maxY));
      this.position = {
        x: newX,
        y: newY
      };
      const img = this.findFiles(item);
      img.x = newX;
      img.y = newY;
      console.log("移动到:", this.dataType);
    },
    // 触摸结束
    handleTouchEnd() {
      this.isDragging = false;
      console.log("触摸结束，最终位置:", this.position);
    },
    // 缩放
    scaleItem(e, it) {
      console.log(e, it, 123);
      if (e && e.touches && e.touches.length === 1) {
        var img = this.findFiles(it);
        const touch = e.touches[0];
        const x = touch.clientX || touch.pageX;
        const y = touch.clientY || touch.pageY;
        if (!img._lastTouch) {
          img._lastTouch = { x, y };
          return;
        }
        const dx = x - img._lastTouch.x;
        const dy = y - img._lastTouch.y;
        let newW = img.w + dx;
        let newH = img.h + dy;
        newW = Math.max(20, Math.min(newW, this.dimensions.outerWidth));
        newH = Math.max(20, Math.min(newH, this.dimensions.outerHeight));
        img.w = newW;
        img.h = newH;
        img._lastTouch = { x, y };
        this.$forceUpdate();
      }
    },
    // 复制一份
    copyItem(e) {
      var img = this.findFiles(e);
      console.log(e, "mmm");
      img.display = false;
      this.dataType.push({
        ...img,
        ids: this.dataType.length + 1,
        name: img.name + (this.dataType.length + 1),
        sox: img.x + 20,
        soy: img.y + 20,
        x: img.x + 20,
        y: img.y + 20,
        display: true
      });
    },
    // 旋转 - 手指拖动旋转
    rotateItem(e, it) {
      const img = this.findFiles(it);
      if (!img || !e.touches || !e.touches.length)
        return;
      const touch = e.touches[0];
      const x = touch.pageX || touch.clientX;
      const y = touch.pageY || touch.clientY;
      const canvasTop = 120;
      const canvasLeft = 20;
      const centerX = canvasLeft + img.x + img.w / 2;
      const centerY = canvasTop + img.y + img.h / 2;
      const deltaX = x - centerX;
      const deltaY = y - centerY;
      const currentAngle = Math.atan2(deltaY, deltaX);
      if (img._startAngle === void 0) {
        img._startAngle = currentAngle;
        img._initialRotate = img.rotate || 0;
        return;
      }
      let angleDiff = currentAngle - img._startAngle;
      if (angleDiff > Math.PI) {
        angleDiff -= 2 * Math.PI;
      } else if (angleDiff < -Math.PI) {
        angleDiff += 2 * Math.PI;
      }
      img.rotate = img._initialRotate + angleDiff;
    },
    // 旋转结束 - 清理临时变量
    rotateEnd(it) {
      const img = this.findFiles(it);
      if (!img)
        return;
      delete img._startAngle;
      delete img._initialRotate;
    },
    recordClick(e) {
      console.log(typeof e);
      if (typeof e == "object") {
        this.record.push(this.dataType.filter((item) => item.ids == e.ids)[0]);
        this.dataType = this.dataType.filter((item) => item.ids !== e.ids);
      } else {
        this.record.push(...this.dataType);
        this.dataType = [];
      }
    },
    disp(e) {
      this.dataType.forEach((element) => {
        if (element.ids == e.ids) {
          element.display = true;
          this.selectedItem = element;
        } else {
          element.display = false;
        }
      });
    },
    // 数据ids匹配
    findFiles(e) {
      return this.dataType.find((f) => f.ids == e.ids);
    },
    // 删除
    deleteItem(it) {
      this.recordClick(it);
    },
    cornerTop(e, it) {
    },
    cornerRight() {
    },
    cornerBtm(e, it) {
    },
    cornerLeft() {
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.dataBack
  }, $data.dataBack ? {
    b: $data.dataBack.url,
    c: $data.dimensions.outerWidth + "px",
    d: $data.dimensions.outerHeight + "px"
  } : {}, {
    e: common_vendor.f($data.dataType, (it, index, i0) => {
      return common_vendor.e({
        a: it.type != "text"
      }, it.type != "text" ? {
        b: Math.abs(it.w) + "px",
        c: Math.abs(it.h) + "px",
        d: (it.h < 0 ? "scaleY(-1)" : "") + " " + (it.w < 0 ? "scaleX(-1)" : ""),
        e: it.url
      } : {
        f: common_vendor.t(it.text),
        g: it.w,
        h: it.h,
        i: it.size + "px",
        j: it.color,
        k: it.textTpey
      }, {
        l: it.display && !$data.editLine
      }, it.display && !$data.editLine ? {
        m: common_vendor.o((e) => $options.cornerTop(e, it), index),
        n: common_vendor.o((e) => $options.cornerRight(e, it), index),
        o: common_vendor.o((e) => $options.cornerBtm(e, it), index),
        p: common_vendor.o((e) => $options.cornerLeft(e, it), index),
        q: $data.del,
        r: common_vendor.o(($event) => $options.deleteItem(it), index),
        s: $data.jia,
        t: common_vendor.o(($event) => $options.copyItem(it), index),
        v: $data.rotate,
        w: common_vendor.o((e) => $options.rotateItem(e, it), index),
        x: common_vendor.o((e) => $options.rotateEnd(it), index),
        y: $data.suof,
        z: common_vendor.o((e) => $options.scaleItem(e, it), index)
      } : {}, {
        A: it.display && $data.editLine == 1
      }, it.display && $data.editLine == 1 ? {
        B: common_vendor.o((e) => $options.handleTouchs(e, it, "top"), index),
        C: common_vendor.o((e) => $options.handleTouchs(e, it, "bottom"), index),
        D: common_vendor.o((e) => $options.handleTouchs(e, it, "left"), index),
        E: common_vendor.o((e) => $options.handleTouchs(e, it, "right"), index)
      } : {}, {
        F: it.display && $data.editLine == 2
      }, it.display && $data.editLine == 2 ? {} : {}, {
        G: it.w + 17 + "px",
        H: it.h + 17 + "px",
        I: it.display ? "2px dashed rgb(0, 0, 0)" : "none",
        J: it.Weights ? "900" : "none",
        K: index,
        L: common_vendor.o((e) => $options.handleTouchStart(e, it), index),
        M: common_vendor.o((e) => $options.handleTouchMove(e, it), index),
        N: common_vendor.o((...args) => $options.handleTouchEnd && $options.handleTouchEnd(...args), index),
        O: `translate(${it.x}px, ${it.y}px) scale(${it.display ? 1.05 : 1}) rotate(${it.rotate}rad)`,
        P: it.w + "px",
        Q: it.h + "px"
      });
    }),
    f: common_vendor.n(!$data.dataBack.url ? "outer-box2" : ""),
    g: $data.dimensions.outerWidth + "px",
    h: $data.dimensions.outerHeight + "px"
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e6d83125"]]);
wx.createComponent(Component);
